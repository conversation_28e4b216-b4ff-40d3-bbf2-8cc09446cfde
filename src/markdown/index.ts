/**
 * Obsidian-Ghost Markdown Parser
 *
 * A clean, simple parser that converts between Markdown and Lexical
 * with support for Obsidian-specific formatting and Ghost-specific nodes.
 */

import { createHeadlessEditor, type LexicalEditor } from '@lexical/headless';
import { $convertFromMarkdownString, $convertToMarkdownString } from '@lexical/markdown';
import { TRANSFORMERS, type TextFormatTransformer } from '@lexical/markdown';

import { HeadingNode, QuoteNode } from '@lexical/rich-text';
import { ListItemNode, ListNode } from '@lexical/list';
import { CodeNode } from '@lexical/code';
import { LinkNode } from '@lexical/link';

/**
 * Obsidian-specific italic transformer for asterisk syntax
 * Enhanced version that handles Obsidian's specific italic behavior
 */
export const OBSIDIAN_ITALIC_STAR: TextFormatTransformer = {
  format: ['italic'],
  tag: '*',
  type: 'text-format',
};

/**
 * Obsidian-specific italic transformer for underscore syntax
 * Enhanced version that handles Obsidian's specific italic behavior
 */
export const OBSIDIAN_ITALIC_UNDERSCORE: TextFormatTransformer = {
  format: ['italic'],
  intraword: false, // Obsidian doesn't allow underscores within words for italic
  tag: '_',
  type: 'text-format',
};

/**
 * Combined transformers including Obsidian-specific ones
 */
const OBSIDIAN_TRANSFORMERS = [
  ...TRANSFORMERS,
  // Add our custom transformers after the default ones
  // Note: Order matters - more specific transformers should come first
];

/**
 * Result of a conversion operation
 */
export interface ConversionResult<T> {
  success: boolean;
  data?: T;
  error?: string;
}

/**
 * Lexical document representation
 */
export interface LexicalDocument {
  root: any; // Will be properly typed later
  nodes: any[]; // Will be properly typed later
}

/**
 * Main Markdown parser class
 */
export class Markdown {
  private editor: LexicalEditor;

  constructor() {
    // Create a headless Lexical editor (no DOM required)
    // Register all the node types we need for markdown parsing
    this.editor = createHeadlessEditor({
      namespace: 'obsidian-ghost-markdown',
      nodes: [
        HeadingNode,
        ListNode,
        ListItemNode,
        QuoteNode,
        CodeNode,
        LinkNode,
      ],
      onError: (error) => {
        console.error('Lexical editor error:', error);
      },
    });
  }

  /**
   * Convert markdown string to Lexical document
   */
  async markdownToLexical(markdown: string): Promise<ConversionResult<LexicalDocument>> {
    try {
      if (typeof markdown !== 'string') {
        return {
          success: false,
          error: 'Input must be a string',
        };
      }

      // Use the same pattern as Lexical tests
      this.editor.update(
        () => {
          $convertFromMarkdownString(markdown, OBSIDIAN_TRANSFORMERS);
        },
        {
          discrete: true,
        }
      );

      // Get the editor state after the update
      const editorState = this.editor.getEditorState();
      const editorStateJSON = editorState.toJSON();
      const document: LexicalDocument = {
        root: editorStateJSON.root, // Extract just the root part
        nodes: [], // Will populate this properly later
      };

      return {
        success: true,
        data: document,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Convert Lexical document to markdown string
   */
  async lexicalToMarkdown(document: LexicalDocument): Promise<ConversionResult<string>> {
    try {
      if (!document || !document.root) {
        return {
          success: false,
          error: 'Invalid document structure',
        };
      }

      // Handle empty documents
      if (!document.root.children || document.root.children.length === 0) {
        return {
          success: true,
          data: '',
        };
      }

      // Restore the editor state from the document
      // parseEditorState expects the full editor state JSON, not just the root
      const fullEditorStateJSON = {
        root: document.root
      };
      const editorState = this.editor.parseEditorState(fullEditorStateJSON);
      this.editor.setEditorState(editorState);

      // Convert to markdown using the same pattern as Lexical tests
      const markdown = this.editor.getEditorState().read(() => {
        return $convertToMarkdownString(OBSIDIAN_TRANSFORMERS);
      });

      return {
        success: true,
        data: markdown,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Clean up resources
   */
  destroy(): void {
    // Clean up the editor if needed
  }
}

/**
 * Convenience functions for one-off conversions
 * These create fresh editor instances for each conversion to avoid state issues
 */

/**
 * Convert markdown to Lexical document
 */
export async function markdownToLexical(markdown: string): Promise<ConversionResult<LexicalDocument>> {
  const parser = new Markdown();
  try {
    return await parser.markdownToLexical(markdown);
  } finally {
    parser.destroy();
  }
}

/**
 * Convert Lexical document to markdown
 */
export async function lexicalToMarkdown(document: LexicalDocument): Promise<ConversionResult<string>> {
  const parser = new Markdown();
  try {
    return await parser.lexicalToMarkdown(document);
  } finally {
    parser.destroy();
  }
}

/**
 * Round-trip test: markdown -> lexical -> markdown
 */
export async function roundTrip(markdown: string): Promise<ConversionResult<string>> {
  const parser = new Markdown();
  try {
    const lexicalResult = await parser.markdownToLexical(markdown);
    if (!lexicalResult.success || !lexicalResult.data) {
      return {
        success: false,
        error: `Failed to convert to Lexical: ${lexicalResult.error}`,
      };
    }

    const markdownResult = await parser.lexicalToMarkdown(lexicalResult.data);
    if (!markdownResult.success || markdownResult.data === undefined) {
      return {
        success: false,
        error: `Failed to convert back to markdown: ${markdownResult.error}`,
      };
    }

    return {
      success: true,
      data: markdownResult.data,
    };
  } finally {
    parser.destroy();
  }
}
